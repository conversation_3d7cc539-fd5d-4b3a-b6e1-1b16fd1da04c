import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";
import { useIntl } from "react-intl";
import { profilePath } from "components/StartAuthentication/constant";

const BackHeader = ({ id }: { id?: string }) => {
  const intl = useIntl();
  const router = useRouter();

  return (
    <div className="flex items-center justify-between mb-8">
      <div className="cursor-pointer" onClick={() => router.push(profilePath)}>
        <LeftOutlined style={{ fontSize: "24px" }} />
      </div>
      {id && (
        <div className="text-white font-bold text-2xl">
          {intl.formatMessage({ id })}
        </div>
      )}
      <div></div>
    </div>
  );
};

export default BackHeader;
