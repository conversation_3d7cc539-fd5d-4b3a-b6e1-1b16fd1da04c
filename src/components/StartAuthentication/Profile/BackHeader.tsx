import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";
import { useIntl } from "react-intl";

const BackHeader = ({ id }: { id: string }) => {
  const intl = useIntl();
  const router = useRouter();

  return (
    <div className="flex items-center justify-between mb-8">
      <div className="cursor-pointer" onClick={() => router.back()}>
        <LeftOutlined style={{ fontSize: "24px" }} />
      </div>
      <div className="text-white font-bold text-2xl">
        {intl.formatMessage({ id })}
      </div>
      <div></div>
    </div>
  );
};

export default BackHeader;
