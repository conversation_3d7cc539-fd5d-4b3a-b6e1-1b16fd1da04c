import React from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import clsx from "clsx";
import { RightOutlined } from "@ant-design/icons";

import { getLocaleTitle } from "utils/locale";

const MenuItem = ({ itemList }: { itemList: any[] }) => {
  const intl = useIntl();
  const router = useRouter();
  const { locale } = router;

  const handleMenuItemClick = (item: any) => {
    if (item.target) {
      window.open(item.href, item.target);
      return;
    }

    if (item.href) {
      router.push(item.href);
    }
  };
  return (
    <div className="rounded-lg overflow-hidden divide-y divide-gray-300">
      {itemList.map((item: any) => {
        const isLanguageItem = item.id === "language";
        const currentLanguage = getLocaleTitle(locale || "en");

        return (
          <div
            key={item.id}
            onClick={() => {
              if (item.onClick) {
                item.onClick();
              } else {
                handleMenuItemClick(item);
              }
            }}
            className={clsx(
              "h-16 pr-6 pl-4 py-2 flex items-center justify-between bg-dark-300 cursor-pointer hover:bg-[#3a3d4a] transition-colors",
              !item.icon && "pl-6"
            )}
          >
            <div className="flex items-center gap-4">
              {item.icon && (
                <div className="w-12 h-12">
                  <img
                    src={item.icon}
                    alt={item.titleKey}
                    className="w-full h-full object-contain"
                  />
                </div>
              )}
              <div className="text-white font-bold">
                {intl.formatMessage({ id: item.titleKey })}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {isLanguageItem && (
                <span className="font-bold text-sm">{currentLanguage}</span>
              )}
              <RightOutlined />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default MenuItem;
