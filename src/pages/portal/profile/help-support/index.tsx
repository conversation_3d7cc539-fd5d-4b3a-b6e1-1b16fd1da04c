import React, { useMemo } from "react";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";

const HelpSupport = () => {
  // Help & Support menu items configuration
  const helpSupportMenuItems = useMemo(
    () => [
      {
        id: "faq",
        titleKey: "help_support_menu_faq",
        href: "/faq",
        target: "_blank",
      },
      {
        id: "feedback_and_help",
        titleKey: "help_support_menu_feedback_and_help",
        onClick: () => {},
      },
      {
        id: "email_us",
        titleKey: "help_support_menu_contact_us_by_email",
        onClick: () => {},
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_help_support" />
      <MenuItem itemList={helpSupportMenuItems} />
    </StartAuthenticationHeader>
  );
};

export default HelpSupport;
