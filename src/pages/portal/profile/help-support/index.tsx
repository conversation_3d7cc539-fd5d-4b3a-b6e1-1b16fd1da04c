import React, { useMemo, useState } from "react";
import { useIntl } from "react-intl";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import AppModal from "components/AppModal";
import { APP_SUPPORT_EMAIL } from "constants/app";

const HelpSupport = () => {
  const intl = useIntl();
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);

  // Help & Support menu items configuration
  const helpSupportMenuItems = useMemo(
    () => [
      {
        id: "faq",
        titleKey: "help_support_menu_faq",
        href: "/faq",
        target: "_blank",
      },
      {
        id: "feedback_and_help",
        titleKey: "help_support_menu_feedback_and_help",
        onClick: () => {},
      },
      {
        id: "email_us",
        titleKey: "help_support_menu_contact_us_by_email",
        onClick: () => {
          setIsEmailModalOpen(true);
        },
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_help_support" />
      <MenuItem itemList={helpSupportMenuItems} />

      {/* Email Contact Modal */}
      <AppModal
        isModalOpen={isEmailModalOpen}
        setIsModalOpen={setIsEmailModalOpen}
        title=""
      >
        <div className="text-center space-y-6 py-4">
          {/* Icon */}
          <div className="flex justify-center">
            <img
              src="/order/icon_authentic.png"
              alt="email"
              className="w-14 h-6"
            />
          </div>

          {/* Title */}
          <div className="text-white text-xl font-bold mb-4">
            {intl.formatMessage({ id: "email_modal_title" })}
          </div>

          {/* Subtitle */}
          <div className="text-gray-400 text-base mb-6">
            {intl.formatMessage({ id: "email_modal_subtitle_1" })}
            <br />
            <span className="font-bold">{APP_SUPPORT_EMAIL}</span>{" "}
            {intl.formatMessage({ id: "email_modal_subtitle_2" })}
            <br />
            {intl.formatMessage({ id: "email_modal_subtitle_3" })}
          </div>

          {/* Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => {
                navigator.clipboard.writeText(APP_SUPPORT_EMAIL);
                window.open(`mailto:${APP_SUPPORT_EMAIL}`, "_top");
                setIsEmailModalOpen(false);
              }}
              className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              {intl.formatMessage({ id: "email_modal_open_email_button" })}
            </button>

            <button
              onClick={() => setIsEmailModalOpen(false)}
              className="w-full border border-gray-400 text-white font-medium py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors"
            >
              {intl.formatMessage({ id: "modal_done_button" })}
            </button>
          </div>
        </div>
      </AppModal>
    </StartAuthenticationHeader>
  );
};

export default HelpSupport;
