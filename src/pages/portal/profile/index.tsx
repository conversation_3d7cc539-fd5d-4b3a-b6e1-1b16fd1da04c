import React, { useMemo } from "react";
import { useRouter } from "next/router";

import { userSignOut } from "actions/app";
import { PATH_ROUTE } from "constants/app";
import useAppDispatch from "hooks/useAppDispatch";
import { showSuccessPopupMessage } from "utils/message";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import {
  aboutLegitAppPath,
  accountPath,
  bookmarksPath,
  legalInformationPath,
  orderHistoryPath,
} from "components/StartAuthentication/constant";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";

const Profile = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  // Profile menu items configuration
  const profileMenuItems = useMemo(
    () => [
      {
        id: "account",
        icon: "/profile/icon_account.png",
        titleKey: "profile_menu_account",
        href: accountPath,
      },
      {
        id: "bookmarks",
        icon: "/profile/icon_bookmarks.png",
        titleKey: "profile_menu_bookmarks",
        href: bookmarksPath,
      },
      {
        id: "order_history",
        icon: "/profile/icon_order_history.png",
        titleKey: "profile_menu_order_history",
        href: orderHistoryPath,
      },
      {
        id: "business_inquiries",
        icon: "/profile/icon_business_inquiries.png",
        titleKey: "profile_menu_business_inquiries",
        href: "/contact",
        target: "_blank",
      },
      {
        id: "help_support",
        icon: "/profile/icon_helps_support.png",
        titleKey: "profile_menu_help_support",
        href: "/contact",
        target: "_blank",
      },
      {
        id: "legal_information",
        icon: "/profile/icon_legal_information.png",
        titleKey: "profile_menu_legal_information",
        href: legalInformationPath,
      },
      {
        id: "about_legit_app",
        icon: "/profile/icon_about_legit_app.png",
        titleKey: "profile_menu_about_legit_app",
        href: aboutLegitAppPath,
      },
    ],
    []
  );

  const handleLogout = () => {
    dispatch(userSignOut());
    showSuccessPopupMessage("You have successfully logged out.");
    router.push(PATH_ROUTE.LOGIN);
  };

  return (
    <div className="min-h-screen">
      <StartAuthenticationHeader>
        <div className="my-8">
          {/* Profile Menu Items */}
          <div className="space-y-6">
            {/*  Account & Language */}
            <MenuItem itemList={profileMenuItems.slice(0, 1)} />

            {/* Bookmarks & Order History */}
            <MenuItem itemList={profileMenuItems.slice(2, 4)} />

            {/* Business, Help, Legal, About */}
            <MenuItem itemList={profileMenuItems.slice(4)} />
          </div>
        </div>
        <div
          onClick={handleLogout}
          className="cursor-pointer text-white text-center py-3 border border-gray-100 rounded-sm"
        >
          LOG OUT
        </div>
      </StartAuthenticationHeader>
    </div>
  );
};

export default Profile;
