import React from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";

import StartAuthenticationHeader from "components/StartAuthentication/Header";

const Account = () => {
  const router = useRouter();
  const intl = useIntl();

  return (
    <StartAuthenticationHeader>
      <div className="flex items-center justify-between">
        <div className="cursor-pointer" onClick={() => router.back()}>
          <LeftOutlined style={{ fontSize: "24px" }} />
        </div>
        <div className="text-white font-bold text-2xl">
          {intl.formatMessage({ id: "profile_menu_account" })}
        </div>
        <div></div>
      </div>
      <div className="my-8">Account</div>
    </StartAuthenticationHeader>
  );
};

export default Account;
