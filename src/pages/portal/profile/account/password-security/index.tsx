import React, { useState } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import apiCore from "utils/apiCore";
import useUserHook from "hooks/useUser";
import { parseError } from "utils/error";
import { showSuccessPopupMessage, showErrorPopupMessage } from "utils/message";

interface PasswordValidation {
  hasLetter: boolean;
  hasNumber: boolean;
  hasLength: boolean;
}

const PasswordSecurity = () => {
  const intl = useIntl();
  const router = useRouter();
  const { accessToken } = useUserHook();

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [isSubmitting, setIsSubmitting] = useState(false);

  const [newPasswordValidation, setNewPasswordValidation] =
    useState<PasswordValidation>({
      hasLetter: false,
      hasNumber: false,
      hasLength: false,
    });

  const validatePassword = (password: string): PasswordValidation => {
    return {
      hasLetter: /[a-zA-Z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasLength: password.length >= 9,
    };
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setNewPassword(password);
    setNewPasswordValidation(validatePassword(password));
  };

  const isNewPasswordValid =
    newPasswordValidation.hasLetter &&
    newPasswordValidation.hasNumber &&
    newPasswordValidation.hasLength;

  const isFormValid =
    currentPassword.trim() !== "" &&
    isNewPasswordValid &&
    newPassword === confirmPassword;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!accessToken) {
      router.push("/login");
      return;
    }

    if (!isFormValid) {
      return;
    }

    try {
      setIsSubmitting(true);

      await apiCore.patch(
        null,
        "v1/me/password",
        {
          current_password: currentPassword,
          new_password: newPassword,
        },
        accessToken
      );

      showSuccessPopupMessage(
        intl.formatMessage({ id: "password_change_success" })
      );

      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      setNewPasswordValidation({
        hasLetter: false,
        hasNumber: false,
        hasLength: false,
      });
    } catch (error) {
      console.error("Error changing password:", error);
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const PasswordInput = ({
    id,
    label,
    value,
    onChange,
    placeholder,
  }: {
    id: string;
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
  }) => (
    <div className="mb-6">
      <label htmlFor={id} className="block mb-2 text-sm text-white">
        {label}
      </label>
      <div className="relative">
        <input
          id={id}
          type="password"
          value={value}
          onChange={onChange}
          className="outline-none w-full bg-transparent border border-gray-600 rounded-lg px-4 py-3 pr-12 text-white"
          required
          placeholder={placeholder}
        />
      </div>
    </div>
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="account_menu_change_password" />

      <div>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Current Password */}
          <PasswordInput
            id="current-password"
            label={intl.formatMessage({ id: "current_password_label" })}
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            placeholder={intl.formatMessage({
              id: "current_password_placeholder",
            })}
          />

          {/* New Password */}
          <PasswordInput
            id="new-password"
            label={intl.formatMessage({ id: "new_password_label" })}
            value={newPassword}
            onChange={handleNewPasswordChange}
            placeholder={intl.formatMessage({ id: "new_password_placeholder" })}
          />

          {/* Password validation feedback */}
          {newPassword && (
            <div className="text-sm">
              {isNewPasswordValid ? (
                <p className="text-green-500">
                  {intl.formatMessage({ id: "password_validation_success" })}
                </p>
              ) : (
                <p className="text-gray-400">
                  {intl.formatMessage({
                    id: "password_validation_requirements",
                  })}
                </p>
              )}
            </div>
          )}

          {/* Confirm Password */}
          <PasswordInput
            id="confirm-password"
            label={intl.formatMessage({ id: "confirm_password_label" })}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder={intl.formatMessage({
              id: "confirm_password_placeholder",
            })}
          />

          {/* Password match validation */}
          {confirmPassword && newPassword !== confirmPassword && (
            <p className="text-red-500 text-sm">
              {intl.formatMessage({ id: "password_mismatch_error" })}
            </p>
          )}

          {/* Submit Button */}
          <div className="pt-8">
            <button
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className={`w-full py-3 px-4 rounded-full font-bold text-white ${
                isFormValid && !isSubmitting
                  ? "bg-btn-gradient hover:opacity-90"
                  : "bg-gray-600 cursor-not-allowed"
              }`}
            >
              {isSubmitting
                ? intl.formatMessage({ id: "updating_password" })
                : intl.formatMessage({ id: "confirm_button" })}
            </button>
          </div>
        </form>
      </div>
    </StartAuthenticationHeader>
  );
};

export default PasswordSecurity;
