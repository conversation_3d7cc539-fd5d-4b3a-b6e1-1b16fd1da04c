import React, { useState } from "react";
import { Form, Input, Checkbox, Button, message } from "antd";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import apiCore from "utils/apiCore";
import { parseError } from "utils/error";
import {
  PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS,
  PRODUCT_CATEGORY_ID_SNEAKERS,
  PRODUCT_CATEGORY_ID_LUXURY_CLOTHING,
  PRODUCT_CATEGORY_ID_LUXURY_SHOES,
  PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES,
  PRODUCT_CATEGORY_ID_LUXURY_WATCHES,
  PRODUCT_CATEGORY_ID_STREETWEAR,
  PRODUCT_CATEGORY_ID_TOYS_FIGURES,
  PRODUCT_CATEGORY_ID_TRADING_CARDS,
  PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS,
} from "constants/app";

interface BusinessInquiryFormData {
  first_name: string;
  last_name: string;
  company: string;
  company_email: string;
  job_title: string;
  phone_number: string;
  monthly_authentication_count: number;
  category_interested: string[];
  content: string;
}

const BusinessInquiries = () => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "" } = router;
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categoryOptions = [
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS.toString(),
      label: intl.formatMessage({ id: "category_luxury_handbags" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_SNEAKERS.toString(),
      label: intl.formatMessage({ id: "category_sneakers" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_CLOTHING.toString(),
      label: intl.formatMessage({ id: "category_luxury_clothing" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_SHOES.toString(),
      label: intl.formatMessage({ id: "category_luxury_shoes" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES.toString(),
      label: intl.formatMessage({ id: "category_luxury_accessories" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_WATCHES.toString(),
      label: intl.formatMessage({ id: "category_luxury_watches" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_STREETWEAR.toString(),
      label: intl.formatMessage({ id: "category_streetwear" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_TOYS_FIGURES.toString(),
      label: intl.formatMessage({ id: "category_toys_figures" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_TRADING_CARDS.toString(),
      label: intl.formatMessage({ id: "category_trading_cards" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS.toString(),
      label: intl.formatMessage({ id: "category_cosmetic_products" }),
    },
  ];

  const onSubmit = async (values: BusinessInquiryFormData) => {
    try {
      setIsSubmitting(true);

      const submitData = {
        first_name: values.first_name,
        last_name: values.last_name,
        company: values.company || "",
        company_email: values.company_email,
        job_title: values.job_title || "",
        country: "Hong Kong", // Default country as shown in API example
        phone_country_code: "+852", // Default country code
        phone_number: values.phone_number || "",
        monthly_authentication_count: values.monthly_authentication_count,
        category_interested: values.category_interested
          ? values.category_interested.join(", ")
          : "",
        subject: "Business Inquiry", // Default subject
        content: values.content || "",
      };

      await apiCore.post(null, "v1/contact", submitData);

      message.success(
        intl.formatMessage({ id: "contact_page_success_message" })
      );
      form.resetFields();
    } catch (error) {
      message.error(parseError(error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_business_inquiries" />

      <div className="px-4 pb-20">
        {/* Header Text */}
        <div className="text-gray-100 text-sm mb-6 leading-relaxed">
          {intl.formatMessage({ id: "business_inquiry_description" })}
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmit}
          className="space-y-4"
          requiredMark={false}
        >
          {/* Name Field */}
          <Form.Item
            name={
              ["zh-Hant", "zh-Hans"].includes(locale)
                ? "first_name"
                : "last_name"
            }
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({
                  id:
                    locale === "zh-Hant" || locale === "zh-Hans"
                      ? "contact_page_last_name"
                      : "contact_page_first_name",
                })}
              </span>
            }
            rules={[{ required: true, message: "" }]}
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Last Name Field */}
          <Form.Item
            name={
              locale === "zh-Hant" || locale === "zh-Hans"
                ? "first_name"
                : "last_name"
            }
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({
                  id:
                    locale === "zh-Hant" || locale === "zh-Hans"
                      ? "contact_page_first_name"
                      : "contact_page_last_name",
                })}
              </span>
            }
            rules={[{ required: true, message: "" }]}
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Company Name */}
          <Form.Item
            name="company"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({ id: "contact_page_company" })}
              </span>
            }
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Job Title */}
          <Form.Item
            name="job_title"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({ id: "contact_page_job_title" })}
              </span>
            }
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Phone Number */}
          <Form.Item
            name="phone_number"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({ id: "business_inquiry_phone_label" })}
              </span>
            }
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Company Email */}
          <Form.Item
            name="company_email"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({ id: "contact_page_company_email" })}
              </span>
            }
            rules={[
              { required: true, message: "" },
              { type: "email", message: "" },
            ]}
          >
            <Input
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Monthly Authentication Count */}
          <Form.Item
            name="monthly_authentication_count"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({
                  id: "contact_page_monthly_authentication_count",
                })}
              </span>
            }
            rules={[{ required: true, message: "" }]}
          >
            <Input
              type="number"
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
              }}
            />
          </Form.Item>

          {/* Categories Interested */}
          <Form.Item
            name="category_interested"
            label={
              <span className="text-white text-sm">
                {intl.formatMessage({ id: "contact_page_category_interested" })}
              </span>
            }
          >
            <div className="bg-gray-800 p-4 rounded-lg">
              <Checkbox.Group className="w-full">
                <div className="grid grid-cols-2 gap-4">
                  {categoryOptions.map((option) => (
                    <Checkbox
                      key={option.value}
                      value={option.value}
                      className="text-white"
                      style={{ color: "#fff" }}
                    >
                      <span className="text-white text-sm">{option.label}</span>
                    </Checkbox>
                  ))}
                </div>
              </Checkbox.Group>
            </div>
          </Form.Item>

          {/* How can we help */}
          <Form.Item
            name="content"
            label={
              <span className="text-gray-400 text-sm">
                {intl.formatMessage({ id: "contact_page_how_can_we_help" })}
              </span>
            }
          >
            <Input.TextArea
              rows={4}
              className="bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 resize-none focus:border-gray-400"
              style={{
                backgroundColor: "transparent",
                color: "#fff",
                border: "none",
                borderBottom: "1px solid #4a4a4a",
                borderRadius: "0",
                boxShadow: "none",
                resize: "none",
              }}
            />
          </Form.Item>

          {/* Submit Button */}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isSubmitting}
              className="w-full h-12 bg-white text-black font-medium border-0 rounded-lg hover:bg-gray-200"
              style={{
                backgroundColor: "#fff",
                color: "#000",
                border: "none",
              }}
            >
              {intl.formatMessage({ id: "business_inquiry_submit_button" })}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </StartAuthenticationHeader>
  );
};

export default BusinessInquiries;
