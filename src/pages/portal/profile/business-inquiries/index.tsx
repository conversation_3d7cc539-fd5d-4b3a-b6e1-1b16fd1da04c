import React, { useState } from "react";
import { Form, Input, Checkbox, Button, message } from "antd";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import apiCore from "utils/apiCore";
import { parseError } from "utils/error";
import {
  PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS,
  PRODUCT_CATEGORY_ID_SNEAKERS,
  PRODUCT_CATEGORY_ID_LUXURY_CLOTHING,
  PRODUCT_CATEGORY_ID_LUXURY_SHOES,
  PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES,
  PRODUCT_CATEGORY_ID_LUXURY_WATCHES,
  PRODUCT_CATEGORY_ID_STREETWEAR,
  PRODUCT_CATEGORY_ID_TOYS_FIGURES,
  PRODUCT_CATEGORY_ID_TRADING_CARDS,
  PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS,
} from "constants/app";

interface BusinessInquiryFormData {
  first_name: string;
  last_name: string;
  company: string;
  company_email: string;
  job_title: string;
  phone_number: string;
  monthly_authentication_count: number;
  category_interested: string[];
  content: string;
}

const BusinessInquiries = () => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "" } = router;
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Category options based on the constants
  const categoryOptions = [
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_HANDBAGS.toString(),
      label: intl.formatMessage({ id: "category_luxury_handbags" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_SNEAKERS.toString(),
      label: intl.formatMessage({ id: "category_sneakers" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_CLOTHING.toString(),
      label: intl.formatMessage({ id: "category_luxury_clothing" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_SHOES.toString(),
      label: intl.formatMessage({ id: "category_luxury_shoes" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_ACCESSORIES.toString(),
      label: intl.formatMessage({ id: "category_luxury_accessories" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_LUXURY_WATCHES.toString(),
      label: intl.formatMessage({ id: "category_luxury_watches" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_STREETWEAR.toString(),
      label: intl.formatMessage({ id: "category_streetwear" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_TOYS_FIGURES.toString(),
      label: intl.formatMessage({ id: "category_toys_figures" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_TRADING_CARDS.toString(),
      label: intl.formatMessage({ id: "category_trading_cards" }),
    },
    {
      value: PRODUCT_CATEGORY_ID_COSMETIC_PRODUCTS.toString(),
      label: intl.formatMessage({ id: "category_cosmetic_products" }),
    },
  ];

  const onSubmit = async (values: BusinessInquiryFormData) => {
    try {
      setIsSubmitting(true);

      const submitData = {
        first_name: values.first_name,
        last_name: values.last_name,
        company: values.company || "",
        company_email: values.company_email,
        job_title: values.job_title || "",
        country: "Hong Kong", // Default country as shown in API example
        phone_country_code: "+852", // Default country code
        phone_number: values.phone_number || "",
        monthly_authentication_count: values.monthly_authentication_count,
        category_interested: values.category_interested
          ? values.category_interested.join(", ")
          : "",
        subject: "Business Inquiry", // Default subject
        content: values.content || "",
      };

      await apiCore.post(null, "v1/contact", submitData);

      message.success(
        intl.formatMessage({ id: "contact_page_success_message" })
      );
      form.resetFields();
    } catch (error) {
      message.error(parseError(error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_business_inquiries" />

      <div className="px-4 pb-20">
        {/* Custom CSS for floating labels */}
        <style jsx>{`
          .floating-label-input:not(:placeholder-shown) + label,
          .floating-label-input:focus + label {
            transform: translateY(-20px);
            font-size: 0.75rem;
            color: #d1d5db;
          }

          .floating-label-input::placeholder {
            opacity: 0;
            transition: opacity 0.2s;
          }

          .floating-label-input:focus::placeholder {
            opacity: 1;
          }
        `}</style>

        {/* Header Text */}
        <div className="text-white text-sm mb-6 leading-relaxed">
          {intl.formatMessage({ id: "business_inquiry_description" })}
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={onSubmit}
          className="space-y-4"
          requiredMark={false}
        >
          {/* Name Field */}
          <div className="relative mb-6">
            <Form.Item
              name={
                locale === "zh-Hant" || locale === "zh-Hans"
                  ? "last_name"
                  : "first_name"
              }
              rules={[{ required: true, message: "" }]}
              className="mb-0"
            >
              <Input
                placeholder={intl.formatMessage({
                  id:
                    locale === "zh-Hant" || locale === "zh-Hans"
                      ? "contact_page_last_name"
                      : "contact_page_first_name",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({
                id:
                  locale === "zh-Hant" || locale === "zh-Hans"
                    ? "contact_page_last_name"
                    : "contact_page_first_name",
              })}
            </label>
          </div>

          {/* Last Name Field */}
          <div className="relative mb-6">
            <Form.Item
              name={
                locale === "zh-Hant" || locale === "zh-Hans"
                  ? "first_name"
                  : "last_name"
              }
              rules={[{ required: true, message: "" }]}
              className="mb-0"
            >
              <Input
                placeholder={intl.formatMessage({
                  id:
                    locale === "zh-Hant" || locale === "zh-Hans"
                      ? "contact_page_first_name"
                      : "contact_page_last_name",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({
                id:
                  locale === "zh-Hant" || locale === "zh-Hans"
                    ? "contact_page_first_name"
                    : "contact_page_last_name",
              })}
            </label>
          </div>

          {/* Company Name */}
          <div className="relative mb-6">
            <Form.Item name="company" className="mb-0">
              <Input
                placeholder={intl.formatMessage({ id: "contact_page_company" })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({ id: "contact_page_company" })}
            </label>
          </div>

          {/* Job Title */}
          <div className="relative mb-6">
            <Form.Item name="job_title" className="mb-0">
              <Input
                placeholder={intl.formatMessage({
                  id: "contact_page_job_title",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({ id: "contact_page_job_title" })}
            </label>
          </div>

          {/* Phone Number */}
          <div className="relative mb-6">
            <Form.Item name="phone_number" className="mb-0">
              <Input
                placeholder={intl.formatMessage({
                  id: "business_inquiry_phone_label",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({ id: "business_inquiry_phone_label" })}
            </label>
          </div>

          {/* Company Email */}
          <div className="relative mb-6">
            <Form.Item
              name="company_email"
              rules={[
                { required: true, message: "" },
                { type: "email", message: "" },
              ]}
              className="mb-0"
            >
              <Input
                placeholder={intl.formatMessage({
                  id: "contact_page_company_email",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({ id: "contact_page_company_email" })}
            </label>
          </div>

          {/* Monthly Authentication Count */}
          <div className="relative mb-6">
            <Form.Item
              name="monthly_authentication_count"
              rules={[{ required: true, message: "" }]}
              className="mb-0"
            >
              <Input
                type="number"
                placeholder={intl.formatMessage({
                  id: "contact_page_monthly_authentication_count",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 h-12 focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({
                id: "contact_page_monthly_authentication_count",
              })}
            </label>
          </div>

          {/* Categories Interested */}
          <Form.Item
            name="category_interested"
            label={
              <span className="text-white text-sm">
                {intl.formatMessage({ id: "contact_page_category_interested" })}
              </span>
            }
          >
            <div className="bg-gray-800 p-4 rounded-lg">
              <Checkbox.Group className="w-full">
                <div className="grid grid-cols-2 gap-4">
                  {categoryOptions.map((option) => (
                    <Checkbox
                      key={option.value}
                      value={option.value}
                      className="text-white"
                      style={{ color: "#fff" }}
                    >
                      <span className="text-white text-sm">{option.label}</span>
                    </Checkbox>
                  ))}
                </div>
              </Checkbox.Group>
            </div>
          </Form.Item>

          {/* How can we help */}
          <div className="relative mb-6">
            <Form.Item name="content" className="mb-0">
              <Input.TextArea
                rows={4}
                placeholder={intl.formatMessage({
                  id: "contact_page_how_can_we_help",
                })}
                className="floating-label-input bg-transparent text-white border-0 border-b border-gray-600 rounded-none px-0 resize-none focus:border-gray-400 peer"
                style={{
                  backgroundColor: "transparent",
                  color: "#fff",
                  border: "none",
                  borderBottom: "1px solid #4a4a4a",
                  borderRadius: "0",
                  boxShadow: "none",
                  resize: "none",
                }}
              />
            </Form.Item>
            <label className="absolute left-0 top-3 text-gray-400 text-sm transition-all duration-200 pointer-events-none peer-focus:-top-5 peer-focus:text-xs peer-focus:text-gray-300 peer-valid:-top-5 peer-valid:text-xs peer-valid:text-gray-300">
              {intl.formatMessage({ id: "contact_page_how_can_we_help" })}
            </label>
          </div>

          {/* Submit Button */}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isSubmitting}
              className="w-full h-12 bg-white text-black font-medium border-0 rounded-lg hover:bg-gray-200"
              style={{
                backgroundColor: "#fff",
                color: "#000",
                border: "none",
              }}
            >
              {intl.formatMessage({ id: "business_inquiry_submit_button" })}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </StartAuthenticationHeader>
  );
};

export default BusinessInquiries;
