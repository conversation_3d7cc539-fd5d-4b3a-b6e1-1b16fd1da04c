import React, { useEffect, useState, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import useAppSelector from "hooks/useAppSelector";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import AppPlaceholder from "components/AppPlaceholder";
import AppSpin from "components/AppSpin";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import { orderHistoryPath } from "components/StartAuthentication/constant";
import { LIST_PAGESIZE_24 } from "constants/app";
import { formatDate } from "utils/authHelper";
import { LegitTagOrder } from "types/profile";

const orderStatusMap = {
  SHIP_SUCCESS: "ship_success",
  SHIP_PENDING: "ship_pending",
  ORDER_CANCELLED: "order_cancelled",
};

const AuthTagOrder = () => {
  const intl = useIntl();
  const router = useRouter();
  const { locale, defaultLocale } = router;
  const { accessToken } = useAppSelector((state) => state.app);

  const [authTagOrder, setAuthTagOrder] = useState<LegitTagOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState<number>(0);

  const hasMore = authTagOrder && total ? authTagOrder.length < total : false;
  const fetchAuthTagOrder = useCallback(
    async (offset = 0, append = false) => {
      if (!accessToken) {
        showErrorPopupMessage("You need to login to view your bookmarks");
        router.push("/login");
        return;
      }

      try {
        if (append) {
          setIsLoadMoreLoading(true);
        } else {
          setIsLoading(true);
        }
        setError(null);

        const result = await apiCore.get(
          null,
          "v1/legit_tag/order_history",
          {
            $offset: offset,
            $limit: LIST_PAGESIZE_24,
          },
          accessToken
        );

        if (append) {
          setAuthTagOrder((prev) => [...prev, ...(result.data || [])]);
        } else {
          setAuthTagOrder(result.data || []);
        }
        setTotal(result.total);
      } catch (error) {
        setError(parseError(error).message);
        showErrorPopupMessage(parseError(error).message);
      } finally {
        if (append) {
          setIsLoadMoreLoading(false);
        } else {
          setIsLoading(false);
        }
      }
    },
    [accessToken, router]
  );

  const handleLoadMore = useCallback(() => {
    if (isLoadMoreLoading || !hasMore) return;

    const currentItems = authTagOrder || [];
    fetchAuthTagOrder(currentItems.length, true);
  }, [fetchAuthTagOrder, authTagOrder, isLoadMoreLoading, hasMore]);

  useEffect(() => {
    fetchAuthTagOrder();
  }, [fetchAuthTagOrder]);

  if (isLoading) {
    return (
      <StartAuthenticationHeader>
        <div className="flex justify-center items-center h-64">
          <AppSpin />
        </div>
      </StartAuthenticationHeader>
    );
  }

  if (error) {
    return (
      <StartAuthenticationHeader>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 text-center">
            {intl.formatMessage({ id: "error_loading_auth_tag_order" })}
          </div>
        </div>
      </StartAuthenticationHeader>
    );
  }

  const renderOrderStatus = (status: string) => {
    switch (status) {
      case orderStatusMap.SHIP_SUCCESS:
        return (
          <div className="bg-blue-400 rounded-sm px-1 py-0.5">
            {intl.formatMessage({
              id: "order_history_menu_auth_tag_order_ship_success",
            })}
          </div>
        );
      case orderStatusMap.SHIP_PENDING:
        return (
          <div className="bg-yellow-400 rounded-sm px-2 py-0.5">
            {intl.formatMessage({
              id: "order_history_menu_auth_tag_order_ship_pending",
            })}
          </div>
        );
      case orderStatusMap.ORDER_CANCELLED:
        return (
          <div className="text-white bg-red-500 rounded-sm px-1 py-0.5">
            {intl.formatMessage({
              id: "order_history_menu_auth_tag_order_order_cancelled",
            })}
          </div>
        );
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="order_history_menu_auth_tag_order_title"
        onBack={() => router.push(orderHistoryPath)}
      />

      <div className="overflow-y-auto w-full flex flex-col">
        <div>
          {authTagOrder.length === 0 ? (
            <AppPlaceholder
              title={intl.formatMessage({
                id: "order_history_menu_auth_tag_order_no_record_title",
              })}
            />
          ) : (
            <>
              <div className="divide-y divide-gray-200">
                {authTagOrder.map((item) => (
                  <div key={item.id} className="p-4 flex justify-between">
                    <div className="flex-1">
                      <div className="text-gray-100 text-xs">#{item.uuid}</div>
                      <div className="sm:text-base text-sm">
                        <span>{item.legit_tag_quantity}</span>
                        <span>
                          {intl.formatMessage({
                            id: "order_history_menu_auth_tag_order_tag_quantity",
                          })}
                        </span>
                        <span className="ml-1">
                          {item.legit_tag_type === "luxe_tag"
                            ? "LUXE TAG"
                            : "KICKS TAG"}
                        </span>
                        <span className="ml-1">x 1</span>
                      </div>
                      <div className="text-gray-100 text-[10px]">
                        <div>
                          {intl.formatMessage({
                            id: "start_authentication_page_order_create_at",
                          })}{" "}
                          {formatDate(locale, defaultLocale, item.created_at)}
                        </div>
                        <div>
                          {intl.formatMessage({
                            id: "start_authentication_page_order_update_at",
                          })}{" "}
                          {formatDate(locale, defaultLocale, item.updated_at)}
                        </div>
                      </div>
                      <div className="border-t border-gray-200 py-1 mt-2">
                        <div className="text-[10px] flex gap-1">
                          <div>{item.recipient_name}</div>
                          <div>{item.recipient_phone_country_code}</div>
                          <div>{item.recipient_phone_number}</div>
                        </div>
                        <div className="text-[10px] flex gap-1">
                          <div>{item.recipient_address1}</div>
                          {item.recipient_address2 && (
                            <div>{item.recipient_address2}</div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-[10px] text-black">
                      {renderOrderStatus(item.status)}
                    </div>
                  </div>
                ))}
              </div>

              {hasMore && (
                <div className="mt-4">
                  <AppListLoadMoreCard
                    onClick={handleLoadMore}
                    loading={isLoadMoreLoading}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default AuthTagOrder;
